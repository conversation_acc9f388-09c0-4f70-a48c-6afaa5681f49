// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AddIcon: typeof import('./src/components/icons/AddIcon.vue')['default']
    AttrContainer: typeof import('./src/components/item/formItem/AttrContainer.vue')['default']
    AttrEmptyIcon: typeof import('./src/components/icons/AttrEmptyIcon.vue')['default']
    AttributeContainer: typeof import('./src/components/container/AttributeContainer.vue')['default']
    AudioIcon: typeof import('./src/components/icons/AudioIcon.vue')['default']
    AudioItem: typeof import('./src/components/item/trackItem/template/AudioItem.vue')['default']
    AudioResourceItem: typeof import('./src/components/item/resourcesItem/AudioResourceItem.vue')['default']
    Canvas: typeof import('./src/components/Canvas.vue')['default']
    CanvasPlayer: typeof import('./src/components/container/CanvasPlayer.vue')['default']
    ColorPicker: typeof import('./src/components/item/formItem/color/ColorPicker.vue')['default']
    DeleteIcon: typeof import('./src/components/icons/DeleteIcon.vue')['default']
    EffectItem: typeof import('./src/components/item/trackItem/template/EffectItem.vue')['default']
    EffectsIcon: typeof import('./src/components/icons/EffectsIcon.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElMenuItemGroup: typeof import('element-plus/es')['ElMenuItemGroup']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    EmptyAttrIcon: typeof import('./src/components/icons/EmptyAttrIcon.vue')['default']
    FilterIcon: typeof import('./src/components/icons/FilterIcon.vue')['default']
    FilterItem: typeof import('./src/components/item/trackItem/template/FilterItem.vue')['default']
    FormItem: typeof import('./src/components/item/formItem/FormItem.vue')['default']
    HeaderContainer: typeof import('./src/components/container/HeaderContainer.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    IconCommunity: typeof import('./src/components/icons/IconCommunity.vue')['default']
    IconDocumentation: typeof import('./src/components/icons/IconDocumentation.vue')['default']
    IconEcosystem: typeof import('./src/components/icons/IconEcosystem.vue')['default']
    IconSupport: typeof import('./src/components/icons/IconSupport.vue')['default']
    IconTooling: typeof import('./src/components/icons/IconTooling.vue')['default']
    ImageIcon: typeof import('./src/components/icons/ImageIcon.vue')['default']
    ImageItem: typeof import('./src/components/item/trackItem/template/ImageItem.vue')['default']
    ItemList: typeof import('./src/components/ItemList.vue')['default']
    Loading: typeof import('./src/components/Loading.vue')['default']
    MenuList: typeof import('./src/components/MenuList.vue')['default']
    OtherResource: typeof import('./src/components/item/resourcesItem/OtherResource.vue')['default']
    Player: typeof import('./src/components/item/player/Player.vue')['default']
    PlayerContro: typeof import('./src/components/item/player/PlayerControl.vue')['default']
    PlayerControl: typeof import('./src/components/item/player/PlayerControl.vue')['default']
    PlayerMoveable: typeof import('./src/components/item/player/PlayerMoveable.vue')['default']
    RedoIcon: typeof import('./src/components/icons/RedoIcon.vue')['default']
    ResourcesContainer: typeof import('./src/components/container/ResourcesContainer.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SplitIcon: typeof import('./src/components/icons/SplitIcon.vue')['default']
    SplitLine: typeof import('./src/components/SplitLine.vue')['default']
    SubIcon: typeof import('./src/components/icons/SubIcon.vue')['default']
    SubList: typeof import('./src/components/SubList.vue')['default']
    TextIcon: typeof import('./src/components/icons/TextIcon.vue')['default']
    TextItem: typeof import('./src/components/item/trackItem/template/TextItem.vue')['default']
    TheWelcome: typeof import('./src/components/TheWelcome.vue')['default']
    TimeLine: typeof import('./src/components/item/trackItem/TimeLine.vue')['default']
    TrackContainer: typeof import('./src/components/container/TrackContainer.vue')['default']
    TrackContro: typeof import('./src/components/item/trackItem/TrackContro.vue')['default']
    TrackHandler: typeof import('./src/components/item/trackItem/TrackHandler.vue')['default']
    TrackItem: typeof import('./src/components/item/trackItem/TrackItem.vue')['default']
    TrackLine: typeof import('./src/components/item/trackItem/TrackLine.vue')['default']
    TrackList: typeof import('./src/components/TrackList.vue')['default']
    TrackListIcon: typeof import('./src/components/item/trackItem/TrackListIcon.vue')['default']
    TrackPlayPoint: typeof import('./src/components/item/trackItem/TrackPlayPoint.vue')['default']
    TransitionIcon: typeof import('./src/components/icons/TransitionIcon.vue')['default']
    TransitionItem: typeof import('./src/components/item/trackItem/template/TransitionItem.vue')['default']
    UndoIcon: typeof import('./src/components/icons/UndoIcon.vue')['default']
    Video: typeof import('./src/components/icons/Video.vue')['default']
    VideoCanvas: typeof import('./src/components/item/trackItem/VideoCanvas.vue')['default']
    VideoFrame: typeof import('./src/components/item/trackItem/VideoFrame.vue')['default']
    VideoFrames: typeof import('./src/components/item/trackItem/VideoFrames.vue')['default']
    VideoIcon: typeof import('./src/components/icons/VideoIcon.vue')['default']
    VideoItem: typeof import('./src/components/item/trackItem/template/VideoItem.vue')['default']
    WelcomeItem: typeof import('./src/components/WelcomeItem.vue')['default']
  }
}
