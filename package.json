{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.0.0", "license": "Apache 2.0", "private": true, "homepage": "https://github.com/Cc-Edit/CcClip", "scripts": {"dev": "vite", "dev-ssl": "vite", "build": "run-p type-check build-only", "preview": "vite preview", "test:unit": "vitest --environment jsdom --root src/", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .gitignore", "lint-fix": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "prepare": "husky install"}, "dependencies": {"@ckpack/vue-color": "^1.4.1", "@element-plus/icons-vue": "^2.0.10", "axios": "^1.3.2", "element-plus": "^2.2.29", "lodash-es": "^4.17.21", "pinia": "^2.0.28", "vue": "^3.2.45", "vue-hooks-plus": "1.6.0-alpha.2", "vue-router": "^4.1.6", "vue3-moveable": "^0.18.1"}, "devDependencies": {"@ffmpeg/core": "^0.11.0", "@ffmpeg/ffmpeg": "^0.11.6", "@rushstack/eslint-patch": "^1.1.4", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/line-clamp": "^0.4.2", "@tailwindcss/typography": "^0.5.9", "@types/jsdom": "^20.0.1", "@types/lodash-es": "^4.17.6", "@types/node": "^18.11.18", "@types/webpack-env": "^1.18.0", "@vitejs/plugin-vue": "^4.0.0", "@vitejs/plugin-basic-ssl": "^1.0.1", "@vue-hooks-plus/resolvers": "^1.2.1", "@vue/eslint-config-typescript": "^11.0.0", "@vue/test-utils": "^2.2.6", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.13", "cssnano": "^5.1.14", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.3.0", "husky": "^8.0.3", "jsdom": "^20.0.3", "npm-run-all": "^4.1.5", "postcss": "^8.4.21", "postcss-import": "^15.1.0", "postcss-nesting": "^11.1.0", "tailwindcss": "^3.2.4", "typescript": "~4.7.4", "unplugin-auto-import": "^0.13.0", "unplugin-icons": "^0.15.2", "unplugin-vue-components": "^0.23.0", "vite": "^4.0.0", "vitest": "^0.25.6", "vue-tsc": "^1.0.12"}}